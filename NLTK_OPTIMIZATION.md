# NLTK Optimization

This document explains the changes made to optimize the NLTK resource downloading process and improve application startup time.

## Problem

The application was taking around 45 seconds to start up, with a significant delay caused by downloading NLTK packages each time the application started. This was happening because:

1. NLTK resources were being downloaded in multiple places:
   - In `rag_handler.py` at module level through the `initialize_nltk()` function
   - In the `tokenize_text()` function in `rag_handler.py` which tries to download packages if tokenization fails
   - In various startup scripts like `fix_embeddings.py`, `ultra_safe_rag.py`, and `safe_retrieval.py`

2. There was no mechanism to check if resources were already downloaded, leading to repeated download attempts.

## Solution

The following changes were made to optimize the NLTK download process:

1. Created a new script `download_nltk_once.py` that:
   - Downloads NLTK resources to a local directory (`nltk_data/`)
   - Creates a flag file (`nltk_download_complete.flag`) to indicate successful download
   - Only downloads resources if they don't already exist

2. Modified `start_app.py` to:
   - Check for the flag file before starting the application
   - Run the download script only if the flag file doesn't exist
   - Skip the download step on subsequent runs

3. Updated the NLTK initialization in `rag_handler.py` to:
   - Check for resources in the local directory first
   - Skip downloading if they're already present
   - Add the local directory to NLTK's search path

4. Updated the `tokenize_text()` function in `rag_handler.py` to:
   - Check for resources in the local directory before attempting to download
   - Only download if resources are not found in the local directory

## Benefits

- **Faster Startup**: The application now starts much faster after the first run since it doesn't need to download NLTK resources each time.
- **Offline Support**: Once resources are downloaded, the application can work offline without needing to download resources again.
- **Reduced Network Usage**: Eliminates unnecessary network requests for resources that are already downloaded.

## How It Works

1. On first run, the application will download all required NLTK resources to the local `nltk_data/` directory and create a flag file.
2. On subsequent runs, the application will detect the flag file and skip the download process.
3. All NLTK functions will use the resources from the local directory instead of trying to download them again.

This approach ensures that NLTK resources are downloaded only once, significantly improving startup time after the initial run.
