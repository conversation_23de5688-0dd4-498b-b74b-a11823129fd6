# MAIAChat - Desktop Version

MAIAChat is a sophisticated Python-based desktop application featuring a multi-agent conversational AI system with cross-platform compatibility for Windows, macOS, and Linux.

## Core Functionality

*   **Multi-Agent Architecture:** Utilizes multiple agents (configured via `agent_config.py`) managed by a `conversation_manager.py` and executed by `worker.py`.
*   **Graphical User Interface (GUI):** Provides a user interface built with PyQt6.
*   **Retrieval-Augmented Generation (RAG):** Incorporates RAG capabilities (`rag_handler.py`) to enhance responses by retrieving information from a knowledge base (`knowledge_base.py`, `knowledge_base/`).
*   **Internet Search:** Includes functionality to perform internet searches (`internet_search.py`).
*   **Conversation Management:** Manages and saves conversation history (`conversation_manager.py`, `conversation_history/`), with support for loading past conversations completely.
*   **Configuration and Caching:** Uses dedicated managers for configuration (`config_manager.py`) and caching (`cache_manager.py`).
*   **Token Counter:** Tracks and displays token usage with separate counts for input+system prompt and output (`token_counter.py`).
*   **Cross-Platform Compatibility:** Includes fixes and optimizations to ensure reliable operation on Windows, macOS, and Linux.

## Recent Improvements (2025-01-27)

### 🎯 Code Organization & Maintainability
- **Centralized Instruction Management**: Moved hardcoded instruction templates to `instruction_templates.py` for better maintainability
- **External Pricing Configuration**: Created `pricing.json` for external API pricing management (foundation for future token counter updates)
- **Dependency Cleanup**: Removed duplicate PyYAML dependency, leveraging pip's transitive dependency handling

### 🔧 Enhanced Features
- **Per-Agent RAG Control**: Individual RAG toggles for each agent, allowing fine-grained control over knowledge base access
- **Dynamic RAG Content Retrieval**: Each agent performs contextual RAG queries based on original prompt plus previous responses
- **Accurate Token Tracking**: Fixed token counting logic with separate user input, system prompt, and output token tracking
- **Comprehensive Logging**: Enhanced logging throughout the RAG system for better debugging and monitoring

## Setup and Usage

### Easy Setup (Recommended)

1.  **Install Dependencies with the Cross-Platform Installer:**
    ```bash
    # On Windows
    python install_dependencies.py

    # On macOS/Linux
    python3 install_dependencies.py
    ```

2.  **Run the Application with Enhanced Error Handling:**
    ```bash
    # On Windows
    python start_app.py

    # On macOS/Linux
    python3 start_app.py
    ```

### Manual Setup (Alternative)

1.  **Install Dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

2.  **Run the Application:**
    ```bash
    python start_app.py  # Use this instead of main.py for better stability
    ```

> **IMPORTANT:** Always use `start_app.py` to run the application instead of `main.py`. The `start_app.py` script includes critical fixes and error handling that prevent crashes, especially when using RAG functionality.

## Key Features

### Ollama Thinking Support ✨ NEW

The application now supports Ollama's thinking feature, which allows compatible models to show their reasoning process:
- **Individual Agent Control**: Enable thinking per agent independently
- **Supported Models**: DeepSeek R1, Qwen 3 (when using Ollama provider)
- **Visual Distinction**: Thinking process shown separately from final response
- **Profile Persistence**: Thinking settings saved with agent configurations

See `docs/THINKING_FEATURE.md` for detailed usage instructions.

### Token Counter

The application includes a sophisticated token counter that:
- Tracks input, system prompt, and output tokens separately
- Displays token counts on screen in real-time
- Calculates estimated costs based on provider-specific pricing
- Maintains token usage history across sessions
- **NEW**: External pricing configuration support via `pricing.json`

### Cross-Platform Fixes

The application includes several fixes to ensure reliable operation across platforms:

- **Safe RAG Processing:** Prevents segmentation faults when loading documents
- **Enhanced Error Handling:** Gracefully recovers from common failure modes
- **Platform-Specific Optimizations:** Automatically detects and adapts to the running platform
- **NLTK Resource Management:** Ensures all required NLTK resources are properly downloaded

## Project Structure

### Core Files
*   `start_app.py`: Main application entry point with enhanced error handling.
*   `main.py`: Original application entry point (now recommended to use `start_app.py` instead).
*   `main_window.py`: Defines the main GUI window.
*   `agent_config.py`: Configuration for different agents.
*   `conversation_manager.py`: Manages interactions between agents and the user.
*   `rag_handler.py`: Handles the RAG process.
*   `knowledge_base.py`: Manages the knowledge base interactions.
*   `internet_search.py`: Handles internet search functionality.
*   `worker.py`: Executes agent tasks.
*   `token_counter.py`: Tracks and displays token usage.

### Configuration Files
*   `instruction_templates.py`: Centralized instruction templates and agent role definitions.
*   `pricing.json`: External API pricing configuration for token cost calculation.
*   `config_manager.py`: Manages application configuration and API keys.
*   `api_key_manager.py`: Centralized API key definitions and management.

### Cross-Platform Support
*   `install_dependencies.py`: Cross-platform dependency installer.
*   `run_app.py`: Helper script used by start_app.py to launch the application.
*   `fix_embeddings.py`: Fixes for embedding generation issues.
*   `safe_rag.py`: Safe version of RAG processing.
*   `safe_retrieval.py`: Safe version of vector retrieval.
*   `ultra_safe_rag.py`: Ultra-safe mode for RAG processing.

### Directories
*   `icons/`: Contains icons used in the GUI.
*   `knowledge_base/`: Stores the FAISS index, embeddings, and related data for RAG.
*   `conversation_history/`: Stores records of past conversations.
*   `cache/`: Caching directory for models and other data.
*   `mcp_config/`: Contains configuration files for MCP (Multi-Channel Processing) servers.

### API Keys and Configuration

The application uses various API keys for services like OpenAI, Google Search, and Brave Search. These keys are stored in:

1. **API Keys**: Stored in `config.json` (for LLM providers)
2. **MCP Server Configuration**: Stored in `mcp_config/servers.json`

For security reasons, these files are excluded from version control. When you first run the application:

1. Copy `mcp_config/servers.template.json` to `mcp_config/servers.json`
2. Edit `mcp_config/servers.json` to add your API keys for search services
3. Use the API Settings in the application UI to configure your LLM provider keys

## Troubleshooting

If you encounter issues:

1. **Check the log files** for detailed error messages:
   - `start_app.log`
   - `fix_embeddings.log`
   - `safe_rag.log`
   - `rag_handler.log`

2. **For platform-specific issues**, see `CROSS_PLATFORM_README.md` for detailed troubleshooting steps.

3. **For RAG-related issues**, see `FIX_README.md` for information about the fixes implemented.

## Development Status

### Recent Updates (2025-01-27)
- ✅ **Code Organization**: Centralized instruction templates and improved maintainability
- ✅ **Dependency Management**: Cleaned up duplicate dependencies and improved package management
- ✅ **External Configuration**: Created foundation for external pricing management
- 🔄 **In Progress**: Token counter external pricing loading implementation

### TODO Progress
- **Completed**: 6/18 tasks (33%)
- **In Progress**: 1/18 tasks (6%)
- **Pending**: 11/18 tasks (61%)

See `TODO.md` for detailed task status and `tasks_completed.md` for implementation history.

## Additional Documentation

- `CROSS_PLATFORM_README.md`: Detailed guide for cross-platform installation and usage.
- `FIX_README.md`: Documentation of the fixes for RAG segmentation faults.
- `OPTIMIZATIONS.md`: Information about performance optimizations.
- `TODO.md`: Current development tasks and progress tracking.
- `tasks_completed.md`: Detailed history of completed implementations.
