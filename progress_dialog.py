# progress_dialog.py
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON>og, QVBoxLayout, QProgressBar, QLabel
from PyQt6.QtCore import Qt


class ProgressDialog(QDialog):
    def __init__(self, title, message, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(300, 100)

        layout = QVBoxLayout()

        self.message_label = QLabel(message)
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)

        layout.addWidget(self.message_label)
        layout.addWidget(self.progress_bar)

        self.setLayout(layout)

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def update_message(self, message):
        self.message_label.setText(message)
