"""
Main Layout component.
This component manages the overall layout of the application.
"""

from PyQt6.QtWidgets import QWidget, QHBoxLayout, QSplitter, QLabel, QVBoxLayout
from PyQt6.QtCore import Qt

from .agent_discussion_panel import AgentDiscussionPanel
from .final_answer_panel import FinalAnswerPanel
from .agent_config_panel import AgentConfigPanel
from .terminal_panel import TerminalPanel


class MainLayout(QWidget):
    """Main layout manager for the application."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        """Initialize the UI components."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create the main splitter
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Create the three main panels
        self.agent_discussion_panel = AgentDiscussionPanel()
        self.final_answer_panel = FinalAnswerPanel()
        self.agent_config_panel = AgentConfigPanel()
        self.terminal_panel = TerminalPanel()

        # Create a vertical splitter for the agent discussion panel and terminal
        self.left_splitter = QSplitter(Qt.Orientation.Vertical)
        self.left_splitter.addWidget(self.agent_discussion_panel)
        self.left_splitter.addWidget(self.terminal_panel)
        self.left_splitter.setSizes([800, 200])  # Give more space to agent discussion

        # Add panels to the main splitter
        self.main_splitter.addWidget(self.left_splitter)
        self.main_splitter.addWidget(self.final_answer_panel)
        self.main_splitter.addWidget(self.agent_config_panel)

        # Set initial sizes (40%, 40%, 20%)
        total_width = 1000  # Arbitrary base width
        self.main_splitter.setSizes([total_width * 0.4, total_width * 0.4, total_width * 0.2])

        # Add splitter to layout
        layout.addWidget(self.main_splitter)

        # Set splitter style
        splitter_style = """
            QSplitter::handle {
                background-color: #E0E0E0;
                width: 2px;
            }
            QSplitter::handle:hover {
                background-color: #2196F3;
            }
        """
        self.main_splitter.setStyleSheet(splitter_style)
        self.left_splitter.setStyleSheet(splitter_style)

    def get_agent_discussion_panel(self):
        """Get the agent discussion panel."""
        return self.agent_discussion_panel

    def get_final_answer_panel(self):
        """Get the final answer panel."""
        return self.final_answer_panel

    def get_agent_config_panel(self):
        """Get the agent configuration panel."""
        return self.agent_config_panel

    def get_terminal_panel(self):
        """Get the terminal panel."""
        return self.terminal_panel
