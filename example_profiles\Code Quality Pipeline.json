{"name": "Code Quality Pipeline", "description": "A six-agent CI-style team dedicated to turning requirements into production-ready, well-tested code.", "general_instructions": "The team must iterate on code in stages:\n1. Requirements → 2. Design → 3. Implementation → 4. Testing → 5. Optimisation → 6. Docs & Release.\nEach agent consumes the previous output, improves it, and hands it off. The final answer should include clean code, passing tests, and concise documentation.", "agents": [{"provider": "OpenAI", "model": "gpt-4o", "instructions": "You are the Requirements Analyst. Clarify the task, list functional requirements, edge cases, and success criteria.", "agent_number": 1}, {"provider": "Anthropic", "model": "claude-3-opus-20250301", "instructions": "You are the Solution Architect. Propose architecture, choose algorithms and data structures, outline modules, and justify design decisions.", "agent_number": 2}, {"provider": "Google GenAI", "model": "gemini-2.0-pro-exp-02-05", "instructions": "You are the Code Implementer. Write clean, idiomatic code that follows the architecture. Include inline comments and placeholder tests.", "agent_number": 3}, {"provider": "Groq", "model": "llama-3.3-70b-versatile", "instructions": "You are the Test Engineer. Expand unit/integration tests for coverage, stub mocks, and ensure the suite fails for known issues.", "agent_number": 4}, {"provider": "DeepSeek", "model": "deepseek-chat", "instructions": "You are the Performance & Security Optimiser. Refactor for efficiency, handle edge-case vulnerabilities, and make the tests pass.", "agent_number": 5}, {"provider": "OpenAI", "model": "gpt-4o", "instructions": "You are the Documentation & Release Manager. Produce concise README, usage examples, change-log, and final, fully working code snippet.", "agent_number": 6}], "internet_enabled": true}