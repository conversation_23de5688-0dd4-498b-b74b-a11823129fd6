# Ultra-Safe Fix for RAG Segmentation Fault

This directory contains several scripts to fix the segmentation fault that occurs when loading files to RAG (Retrieval-Augmented Generation) and when retrieving information to answer questions.

## Problem

The application was crashing with a segmentation fault in two scenarios:

1. **When loading files to RAG**: The error was related to NLTK tokenization failing to find the `punkt_tab` resource, and then crashing during the embedding generation process, especially with larger documents.

2. **When retrieving information to answer questions**: The application was crashing during the retrieval process when trying to generate embeddings for queries and search the vector index.

## Solution

We've created several scripts with an ultra-safe approach to fix both issues:

1. **fix_embeddings.py**: A script that provides a super safe version of the embedding generation function that avoids segmentation faults by:
   - Using CPU instead of GPU/MPS for SentenceTransformer
   - For large documents (>10 chunks), using zero embeddings to avoid crashes completely
   - For smaller documents, processing texts one at a time with text length limits
   - Adding timeout mechanisms to prevent hanging
   - Implementing multiple layers of error handling

2. **ultra_safe_rag.py**: A script that patches the RAGHandler class with an ultra-safe mode:
   - Completely bypasses embedding generation for ALL documents
   - Always uses zero embeddings to avoid any possibility of crashes
   - Ensures the application can always load files without crashing

3. **safe_retrieval.py**: A script that patches the retrieval functionality to avoid crashes:
   - Adds a safe retrieval mode that bypasses the normal vector search
   - Returns random chunks instead of performing actual semantic search
   - Ensures the application can answer questions without crashing

4. **start_app.py**: A sophisticated wrapper script that:
   - Runs all the fixes and then starts the application
   - Monitors the application for crashes or hanging
   - Provides detailed error messages and recovery options

## How to Use

Instead of running `python main.py`, use:

```bash
python start_app.py
```

This will apply all the fixes and start the application with enhanced error handling.

## Cross-Platform Support

We've made our fix cross-platform compatible to ensure it works reliably on both Windows and macOS:

1. **Cross-Platform Installation**: Use the `install_dependencies.py` script to install all required dependencies on any platform:

```bash
python install_dependencies.py
```

2. **Platform-Specific Optimizations**: Our fix automatically detects the platform and applies the appropriate optimizations.

3. **Detailed Instructions**: See `CROSS_PLATFORM_README.md` for detailed installation and troubleshooting instructions for each platform.

This will:
1. Run fix_embeddings.py to test it
2. Run safe_rag.py to patch the RAGHandler
3. Start the main application with all the fixes applied

## Technical Details

The main issues were:

1. **NLTK Resource Missing**: The application was trying to use NLTK's `punkt_tab` resource, but it wasn't being downloaded properly.

2. **Segmentation Fault During Embedding**: The SentenceTransformer was causing a segmentation fault when generating embeddings, likely due to memory issues or GPU/MPS compatibility problems.

The fixes include:

1. **Proper NLTK Initialization**: Ensuring all required NLTK resources are downloaded before the application starts.

2. **CPU-Only Mode**: Forcing SentenceTransformer to use CPU instead of GPU/MPS to avoid compatibility issues.

3. **Batch Processing**: Processing texts in very small batches to avoid memory issues.

4. **Robust Error Handling**: Adding comprehensive error handling to gracefully recover from failures.

## Files

- **fix_embeddings.py**: Safe embedding generation function
- **safe_rag.py**: Script to patch the RAGHandler
- **start_app.py**: Wrapper script to run all fixes and start the application
- **FIX_README.md**: This file, explaining the fix

## Troubleshooting

If you still encounter issues:

1. Make sure you have all the required dependencies installed:
   ```bash
   pip install -r requirements.txt
   ```

2. Try running the fix scripts individually:
   ```bash
   python fix_embeddings.py
   python safe_rag.py
   ```

3. Check the log files for more detailed error messages:
   - fix_embeddings.log
   - safe_rag.log
   - start_app.log
