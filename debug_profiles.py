import logging
logging.basicConfig(level=logging.DEBUG)
from profile_manager import profile_manager

profiles = profile_manager.get_profile_list()
print(f'Total profiles: {len(profiles)}')
print('Example profiles:')
example_count = 0
for name, desc, is_example in profiles:
    if is_example:
        example_count += 1
        print(f'  - {name} (example: {is_example})')
print(f'Total example profiles: {example_count}')
