#!/usr/bin/env python3
# start_app.py - A script to start the application

import os
import sys
import logging
import subprocess

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("start_app.log")
    ]
)

logger = logging.getLogger("StartApp")

def run_main_app():
    """Run the main application by executing main.py."""
    try:
        logger.info("Starting the main application (main.py)...")

        # Get the path to the main.py file
        main_path = os.path.join(os.path.dirname(__file__), 'main.py')

        # Run main.py using the current Python executable
        # Use Popen to allow the main application to run independently
        process = subprocess.Popen([sys.executable, main_path])

        # Wait for the main application process to complete
        # The main application itself will handle its event loop and exit conditions
        process.wait()

        if process.returncode != 0:
            logger.error(f"Main application exited with code {process.returncode}")
            return False
        else:
            logger.info("Main application completed successfully")
            return True
    except Exception as e:
        logger.error(f"Error trying to run main application: {str(e)}")
        return False

if __name__ == "__main__":
    # This script's sole purpose is to launch main.py
    if not run_main_app():
        logger.error("Failed to launch main application")
        sys.exit(1)

    logger.info("start_app.py finished execution.")
    sys.exit(0)
