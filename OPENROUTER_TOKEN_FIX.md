# OpenRouter Token Limit Fix

## Problem Summary

You were experiencing shorter discussions with OpenRouter because the system was hitting the **total context length limit of 40,960 tokens** (input + output combined). The issue was:

1. **Fixed max_tokens**: Setting `max_tokens` to 32,768 left very little room for input tokens
2. **Growing context**: As each agent responded, the input context grew larger
3. **Context overflow**: By Agent 3-5, the input context exceeded the available space
4. **Precision issues**: Even with dynamic calculation, slight estimation errors caused overflow

## Root Cause

OpenRouter has a **total context limit of 40,960 tokens** (input + output), but other providers like OpenAI and Anthropic have separate limits for input and output. The system was treating OpenRouter like other providers.

## Solution Implemented

### 1. Dynamic Token Calculation with Safety Margin

Added `_calculate_dynamic_max_tokens()` method that:
- **Estimates input tokens** using character count ÷ 4 (rough approximation)
- **Uses 95% safety margin** (38,912 tokens) to prevent overflow
- **Calculates available output tokens** = Safe limit - Input tokens
- **Respects user requests** when possible
- **Ensures minimum output** of 1,000 tokens

### 2. Provider-Specific Logic with Safety Margin

```python
# For OpenRouter (total context limit with safety margin)
if provider == "OpenRouter":
    safe_context_limit = int(40960 * 0.95)  # 38,912 tokens
    available_output_tokens = safe_context_limit - estimated_input_tokens
    return min(user_requested_tokens, available_output_tokens, 32768)

# For other providers (output-only limits)
else:
    return min(user_requested_tokens, provider_limit)
```

### 3. Enhanced Token Validation

Updated `_validate_and_adjust_token_limits()` to:
- **Accept agent_input parameter** for dynamic calculation
- **Log input/output token estimates** with safety margin info
- **Provide clear feedback** about adjustments

### 4. Integration Points

Updated all agent processing paths:
- **Sequential processing** in `start_agent_discussion()`
- **Parallel processing** in `_process_agents_in_parallel()`
- **Continue discussion** in `continue_discussion()`

## Test Results

The dynamic calculation with safety margin correctly handles different input sizes:

```
Test 1 - Small input (~7 tokens):
- User requested: 50,000 tokens
- Calculated: 32,768 tokens (limited by OpenRouter max)

Test 2 - Medium input (~7,750 tokens):
- User requested: 50,000 tokens  
- Calculated: 31,162 tokens (within safe limit)

Test 3 - Large input (~22,500 tokens):
- Safe context limit: 38,912 tokens
- User requested: 50,000 tokens
- Calculated: 16,412 tokens
- Total estimated: 38,912 tokens (exactly at safe limit)

Test 4 - Very large input (~35,000 tokens):
- Safe context limit: 38,912 tokens
- User requested: 50,000 tokens
- Calculated: 3,912 tokens
- Total estimated: 38,912 tokens (exactly at safe limit)

Test 5 - Edge case (~24,500 tokens):
- Safe context limit: 38,912 tokens
- User requested: 50,000 tokens
- Calculated: 14,412 tokens
- Total estimated: 38,912 tokens (exactly at safe limit)
```

## Benefits

1. **No more context overflow errors** - Safety margin prevents exceeding limits
2. **Maximum possible output** - Uses 95% of available tokens safely
3. **Transparent feedback** - Shows input/output token estimates with safety info
4. **Backward compatible** - Other providers unaffected
5. **User control preserved** - Still respects user-specified limits when possible
6. **Precision handling** - Accounts for estimation errors

## Usage

The fix is **automatic** - no configuration changes needed. The system will:

1. **Calculate available tokens** based on input size with safety margin
2. **Log the calculation** for transparency
3. **Use optimal token limits** for each agent
4. **Prevent context overflow errors** with 5% safety buffer

## Example Output

```
Agent 1 input: ~1,884 tokens, safe context limit: 38,912 tokens, available output: 32,768 tokens
Agent 1 using 32,768 max tokens

Agent 2 input: ~3,729 tokens, safe context limit: 38,912 tokens, available output: 31,162 tokens  
Agent 2 using 31,162 max tokens

Agent 3 input: ~9,630 tokens, safe context limit: 38,912 tokens, available output: 29,282 tokens
Agent 3 using 29,282 max tokens
```

This ensures that **all 5 agents can complete successfully** without hitting the 40,960 token context limit, with a 5% safety margin to handle estimation errors. 