[{"name": "Brave Search", "url": "https://api.search.brave.com/res/v1/web/search", "description": "Search the web with Brave Search", "enabled": true, "auth_token": "YOUR_BRAVE_SEARCH_API_KEY_HERE", "capabilities": ["web_search", "image_search", "news_search"], "cx": ""}, {"name": "Google Search", "url": "https://www.googleapis.com/customsearch/v1", "description": "Search the web with Google Custom Search API", "enabled": true, "auth_token": "YOUR_GOOGLE_API_KEY_HERE", "capabilities": ["web_search", "image_search", "news_search"], "cx": "YOUR_GOOGLE_SEARCH_ENGINE_ID_HERE"}, {"name": "Local Files", "url": "filesystem://local", "description": "Access and manipulate files on your computer", "enabled": true, "auth_token": "", "capabilities": ["read_file", "write_file", "edit_file", "create_directory", "list_directory", "move_file", "search_files", "get_file_info"], "cx": ""}, {"name": "Filesystem", "url": "filesystem://local", "description": "Access local files within allowed directories.", "enabled": true, "auth_token": "", "capabilities": ["read_file", "write_file", "edit_file", "create_directory", "list_directory", "move_file", "search_files", "get_file_info", "list_allowed_directories"], "cx": ""}, {"name": "Serper Search", "url": "https://api.serper.dev/search", "description": "Serper Search API. Requires API Key.", "enabled": true, "auth_token": "YOUR_SERPER_API_KEY_HERE", "capabilities": ["web_search", "news_search", "image_search"], "cx": ""}, {"name": "GitHub", "url": "https://api.github.com", "description": "GitHub API. Use PAT in token for private access.", "enabled": true, "auth_token": "YOUR_GITHUB_PAT_HERE", "capabilities": ["public_repo_access"], "cx": ""}, {"name": "Context7", "url": "https://api.context7.com/v1", "description": "Access up-to-date documentation and code examples for popular libraries", "enabled": true, "auth_token": "YOUR_CONTEXT7_API_KEY_HERE", "capabilities": ["documentation", "code_examples", "api_reference"], "cx": ""}]